# Galaxy Boot Swagger WebFlux Starter 用户手册

Galaxy Boot Swagger WebFlux Starter 是专为 Spring Boot WebFlux 环境设计的 Swagger UI 组件，提供了与 WebMvc 版本完全兼容的配置方式和功能特性。

## 快速开始

### 1. 添加依赖

在您的 `pom.xml` 中添加以下依赖：

```xml
<dependency>
    <groupId>cn.com.chinastock</groupId>
    <artifactId>galaxy-boot-starter-swagger-webflux</artifactId>
</dependency>
```

### 2. 配置认证信息

在 `application.yml` 中配置用户名和密码：

```yml
galaxy:
  swagger:
    auth:
      username: admin     # 用户名
      password: P@5swOrd  # 密码
```

### 3. 访问 Swagger UI

启动应用后，访问以下地址：

- **Swagger UI**: http://localhost:8080/swagger-ui.html
- **API 文档**: http://localhost:8080/v3/api-docs

访问时需要输入配置的用户名和密码。

## 配置说明

### 基础配置

```yml
galaxy:
  swagger:
    auth:
      username: admin     # Swagger UI 访问用户名
      password: P@5swOrd  # Swagger UI 访问密码
```

### 高级配置

组件会自动配置以下 SpringDoc 属性：

- **Swagger UI 路径**: `/swagger-ui.html`
- **API 文档路径**: `/v3/api-docs`
- **UI 启用状态**: `true`
