<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <groupId>cn.com.chinastock</groupId>
    <artifactId>galaxy-boot-dependencies</artifactId>
    <version>${revision}</version>
    <packaging>pom</packaging>
    <name>Galaxy Boot Dependencies</name>

    <properties>
        <revision>0.2.6-ALPHA2</revision>

        <!-- Spring Boot -->
        <spring-boot.version>3.3.7</spring-boot.version>
        <spring-cloud.version>2023.0.4</spring-cloud.version>

        <!-- SLF4J -->
        <slf4j-bom.version>2.0.16</slf4j-bom.version>
        <log4j-slf4j-impl.version>2.23.1</log4j-slf4j-impl.version>

        <maven-source-plugin.version>3.3.1</maven-source-plugin.version>
        <maven-javadoc-plugin.version>3.11.1</maven-javadoc-plugin.version>
        <flatten-maven-plugin.version>1.6.0</flatten-maven-plugin.version>
        <dokka-maven-plugin.version>1.9.20</dokka-maven-plugin.version>

        <git-changelog-maven-plugin.version>2.2.0</git-changelog-maven-plugin.version>
    </properties>

    <dependencyManagement>
        <dependencies>
            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-dependencies</artifactId>
                <version>${spring-boot.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>

            <dependency>
                <groupId>org.springframework.cloud</groupId>
                <artifactId>spring-cloud-dependencies</artifactId>
                <version>${spring-cloud.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>

            <dependency>
                <groupId>org.springframework.ai</groupId>
                <artifactId>spring-ai-bom</artifactId>
                <version>1.0.0-RC1</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>

            <dependency>
                <groupId>org.slf4j</groupId>
                <artifactId>slf4j-bom</artifactId>
                <version>${slf4j-bom.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>

            <dependency>
                <groupId>cn.com.chinastock</groupId>
                <artifactId>galaxy-boot-core</artifactId>
                <version>${revision}</version>
            </dependency>

            <dependency>
                <groupId>cn.com.chinastock</groupId>
                <artifactId>galaxy-boot-utils</artifactId>
                <version>${revision}</version>
            </dependency>

            <dependency>
                <groupId>cn.com.chinastock</groupId>
                <artifactId>galaxy-boot-security</artifactId>
                <version>${revision}</version>
            </dependency>

            <dependency>
                <groupId>cn.com.chinastock</groupId>
                <artifactId>galaxy-boot-starter-tongweb</artifactId>
                <version>${revision}</version>
            </dependency>

            <dependency>
                <groupId>cn.com.chinastock</groupId>
                <artifactId>galaxy-boot-starter-data-oceanbase</artifactId>
                <version>${revision}</version>
            </dependency>

            <dependency>
                <groupId>cn.com.chinastock</groupId>
                <artifactId>galaxy-boot-starter-feign</artifactId>
                <version>${revision}</version>
            </dependency>

            <dependency>
                <groupId>cn.com.chinastock</groupId>
                <artifactId>galaxy-boot-starter-metrics</artifactId>
                <version>${revision}</version>
            </dependency>

            <dependency>
                <groupId>cn.com.chinastock</groupId>
                <artifactId>galaxy-boot-starter-fastjson</artifactId>
                <version>${revision}</version>
            </dependency>

            <dependency>
                <groupId>cn.com.chinastock</groupId>
                <artifactId>galaxy-boot-starter-swagger</artifactId>
                <version>${revision}</version>
            </dependency>

            <dependency>
                <groupId>cn.com.chinastock</groupId>
                <artifactId>galaxy-boot-starter-kafka</artifactId>
                <version>${revision}</version>
            </dependency>

            <dependency>
                <groupId>cn.com.chinastock</groupId>
                <artifactId>galaxy-boot-starter-redis</artifactId>
                <version>${revision}</version>
            </dependency>

            <dependency>
                <groupId>cn.com.chinastock</groupId>
                <artifactId>galaxy-boot-starter-web</artifactId>
                <version>${revision}</version>
            </dependency>

            <dependency>
                <groupId>cn.com.chinastock</groupId>
                <artifactId>galaxy-boot-starter-webflux</artifactId>
                <version>${revision}</version>
            </dependency>

            <dependency>
                <groupId>cn.com.chinastock</groupId>
                <artifactId>galaxy-boot-starter-webclient</artifactId>
                <version>${revision}</version>
            </dependency>
            
            <dependency>
                <groupId>cn.com.chinastock</groupId>
                <artifactId>galaxy-boot-starter-multi-datasource</artifactId>
                <version>${revision}</version>
            </dependency>

            <dependency>
                <groupId>cn.com.chinastock</groupId>
                <artifactId>galaxy-boot-starter-cache</artifactId>
                <version>${revision}</version>
            </dependency>

            <dependency>
                <groupId>cn.com.chinastock</groupId>
                <artifactId>galaxy-boot-starter-mcp</artifactId>
                <version>${revision}</version>
            </dependency>

            <dependency>
                <groupId>cn.com.chinastock</groupId>
                <artifactId>galaxy-boot-starter-swagger-webflux</artifactId>
                <version>${revision}</version>
            </dependency>
        </dependencies>
    </dependencyManagement>

    <build>
        <plugins>
            <plugin>
                <groupId>org.jetbrains.dokka</groupId>
                <artifactId>dokka-maven-plugin</artifactId>
                <version>${dokka-maven-plugin.version}</version>
                <configuration>
                    <skip>true</skip>
                </configuration>
            </plugin>
            <plugin>
                <groupId>se.bjurr.gitchangelog</groupId>
                <artifactId>git-changelog-maven-plugin</artifactId>
                <version>${git-changelog-maven-plugin.version}</version>
                <configuration>
                    <skip>true</skip>
                </configuration>
            </plugin>
        </plugins>
    </build>

    <profiles>
        <profile>
            <id>release</id>
            <build>
                <plugins>
                    <plugin>
                        <groupId>org.apache.maven.plugins</groupId>
                        <artifactId>maven-source-plugin</artifactId>
                        <version>${maven-source-plugin.version}</version>
                        <executions>
                            <execution>
                                <phase>package</phase>
                                <goals>
                                    <goal>jar-no-fork</goal>
                                </goals>
                            </execution>
                        </executions>
                    </plugin>
                    <plugin>
                        <groupId>org.apache.maven.plugins</groupId>
                        <artifactId>maven-javadoc-plugin</artifactId>
                        <version>${maven-javadoc-plugin.version}</version>
                        <executions>
                            <execution>
                                <phase>package</phase>
                                <goals>
                                    <goal>jar</goal>
                                </goals>
                            </execution>
                        </executions>
                    </plugin>

                    <plugin>
                        <groupId>org.codehaus.mojo</groupId>
                        <artifactId>flatten-maven-plugin</artifactId>
                        <version>${flatten-maven-plugin.version}</version>
                        <configuration>
                            <updatePomFile>true</updatePomFile>
                            <flattenMode>resolveCiFriendliesOnly</flattenMode>
                        </configuration>
                        <executions>
                            <execution>
                                <id>flatten</id>
                                <phase>process-resources</phase>
                                <goals>
                                    <goal>flatten</goal>
                                </goals>
                            </execution>
                            <execution>
                                <id>flatten.clean</id>
                                <phase>clean</phase>
                                <goals>
                                    <goal>clean</goal>
                                </goals>
                            </execution>
                        </executions>
                    </plugin>
                </plugins>
            </build>
        </profile>
        <profile>
            <id>local</id>
            <distributionManagement>
                <repository>
                    <id>local.repo</id>
                    <name>Local Repository</name>
                    <url>file:${project.build.directory}/local-repo</url>
                </repository>
            </distributionManagement>
        </profile>
    </profiles>

    <distributionManagement>
        <repository>
            <id>release</id>
            <name>maven-release</name>
            <url>https://bkrepo.chinastock.com.cn/maven/n614c6/maven-release/</url>
        </repository>
    </distributionManagement>
</project>
