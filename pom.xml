<?xml version="1.0" encoding="UTF-8"?>

<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <parent>
        <groupId>cn.com.chinastock</groupId>
        <artifactId>galaxy-boot-parent</artifactId>
        <version>${revision}</version>
        <relativePath>galaxy-boot-parent/pom.xml</relativePath>
    </parent>

    <artifactId>galaxy-boot</artifactId>
    <packaging>pom</packaging>
    <name>Galaxy Boot</name>
    <description>Galaxy Boot</description>
    <url>https://gitlab.chinastock.com.cn/it-infrastructure/galaxy-boot</url>

    <scm>
        <url>https://gitlab.chinastock.com.cn/it-infrastructure/galaxy-boot</url>
        <connection>
            scm:git:git://gitlab.chinastock.com.cn/it-infrastructure/galaxy-boot.git
        </connection>
        <developerConnection>
            scm:git:ssh://****************************/it-infrastructure/galaxy-boot.git
        </developerConnection>
        <tag>HEAD</tag>
    </scm>

    <properties>
        <!--  项目统一版本号 -->
        <testng.version>7.10.2</testng.version>

        <maven.compiler.source>21</maven.compiler.source>
        <maven.compiler.target>21</maven.compiler.target>
        <!-- Maven Plugin Versions -->
        <maven-compiler-plugin.version>3.13.0</maven-compiler-plugin.version>
        <maven-deploy-plugin.version>3.1.3</maven-deploy-plugin.version>
        <maven-checkstyle-plugin.version>3.6.0</maven-checkstyle-plugin.version>
        <!-- JUnit 5 requires Surefire version 2.22.0 or higher -->
        <maven-surefire-plugin.version>3.5.2</maven-surefire-plugin.version>
        <maven-source-plugin.version>3.3.1</maven-source-plugin.version>

        <!-- 文档化 -->
        <git-changelog-maven-plugin.version>2.2.0</git-changelog-maven-plugin.version>
        <dokka-maven-plugin.version>1.9.20</dokka-maven-plugin.version>
        <maven-javadoc-plugin.version>3.11.1</maven-javadoc-plugin.version>

        <!-- 处理 ${revision} 未能正确处理 -->
        <flatten-maven-plugin.version>1.6.0</flatten-maven-plugin.version>

        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>

        <spring-javaformat.version>0.0.31</spring-javaformat.version>
        <githook-maven-plugin.version>1.0.5</githook-maven-plugin.version>
        <git-build-hook-maven-plugin.version>3.5.0</git-build-hook-maven-plugin.version>
        <exec-maven-plugin.version>3.5.0</exec-maven-plugin.version>
        <build-helper-maven-plugin.version>3.6.0</build-helper-maven-plugin.version>
    </properties>

    <modules>
        <module>galaxy-boot-parent</module>
        <module>galaxy-boot-core</module>
        <module>galaxy-boot-security</module>
        <module>galaxy-boot-utils</module>
<!--        <module>galaxy-boot-test</module>-->

        <module>galaxy-boot-dependencies</module>

        <module>galaxy-boot-starters</module>

        <module>galaxy-boot-examples</module>
        <module>galaxy-boot-docs-generator</module>
    </modules>

    <dependencyManagement>
        <dependencies>
            <dependency>
                <groupId>cn.com.chinastock</groupId>
                <artifactId>galaxy-boot-dependencies</artifactId>
                <version>${project.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>
        </dependencies>
    </dependencyManagement>

    <dependencies>
        <dependency>
            <groupId>org.junit.jupiter</groupId>
            <artifactId>junit-jupiter</artifactId>
            <version>${junit-jupiter.version}</version>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.assertj</groupId>
            <artifactId>assertj-core</artifactId>
            <version>${assertj-core.version}</version>
            <scope>test</scope>
        </dependency>
    </dependencies>

    <build>
        <pluginManagement>
            <plugins>
                <!-- disable auto format from parent -->
                <plugin>
                    <groupId>io.spring.javaformat</groupId>
                    <artifactId>spring-javaformat-maven-plugin</artifactId>
                    <version>${spring-javaformat.version}</version>
                    <executions>
                        <execution>
                            <phase>validate</phase>
                            <configuration>
                                <skip>true</skip>
                            </configuration>
                        </execution>
                    </executions>
                </plugin>
            </plugins>
        </pluginManagement>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-checkstyle-plugin</artifactId>
                <version>${maven-checkstyle-plugin.version}</version>
                <executions>
                    <execution>
                        <id>checkstyle-validation</id>
                        <phase>validate</phase>
                        <goals>
                            <goal>check</goal>
                        </goals>
                        <configuration>
                            <configLocation>checkstyle.xml</configLocation>
                            <includeTestSourceDirectory>true</includeTestSourceDirectory>
                            <consoleOutput>true</consoleOutput>
                            <failsOnError>true</failsOnError>
                            <failOnViolation>true</failOnViolation>
                            <violationSeverity>warning</violationSeverity>
                        </configuration>
                    </execution>
                </executions>
            </plugin>

            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <version>${maven-compiler-plugin.version}</version>
                <inherited>true</inherited>
                <configuration>
                    <source>${maven.compiler.source}</source>
                    <target>${maven.compiler.target}</target>
                </configuration>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-surefire-plugin</artifactId>
                <version>${maven-surefire-plugin.version}</version>
                <inherited>true</inherited>
                <configuration>
                    <forkCount>1</forkCount>
                    <reuseForks>false</reuseForks>
                    <includes>
                        <include>**/*Test*.java</include>
                    </includes>
                </configuration>
            </plugin>

            <plugin>
                <groupId>org.codehaus.mojo</groupId>
                <artifactId>flatten-maven-plugin</artifactId>
                <version>${flatten-maven-plugin.version}</version>
                <configuration>
                    <updatePomFile>true</updatePomFile>
                    <flattenMode>resolveCiFriendliesOnly</flattenMode>
                </configuration>
                <executions>
                    <execution>
                        <id>flatten</id>
                        <phase>process-resources</phase>
                        <goals>
                            <goal>flatten</goal>
                        </goals>
                    </execution>
                    <execution>
                        <id>flatten.clean</id>
                        <phase>clean</phase>
                        <goals>
                            <goal>clean</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
            <plugin>
                <groupId>se.bjurr.gitchangelog</groupId>
                <artifactId>git-changelog-maven-plugin</artifactId>
                <version>${git-changelog-maven-plugin.version}</version>
                <inherited>false</inherited>
                <dependencies>
                    <dependency>
                        <groupId>org.openjdk.nashorn</groupId>
                        <artifactId>nashorn-core</artifactId>
                        <version>15.4</version>
                    </dependency>
                </dependencies>
                <executions>
                    <execution>
                        <id>GenerateGitChangelog</id>
                        <phase>generate-sources</phase>
                        <goals>
                            <goal>git-changelog</goal>
                        </goals>
                        <configuration>
                            <javascriptHelper>
                                <![CDATA[
/**
 * 处理适用于银河项目的提交信息规范，从提交信息中提取提交类型（type），并根据类型进行条件判断。
 *
 * {{#ifGalaxyCommitType commit type="feat"}}
 *   <p>Feature Commit</p>
 * {{else}}
 *   <p>Not a Feature Commit</p>
 * {{/ifGalaxyCommitType}}
 */
Handlebars.registerHelper('ifGalaxyCommitType', function(commit, options) {
    const regString = '^\[[a-zA-Z0-9-]+\] (build|ci|docs|feat|fix|perf|refactor|style|test|chore)(\([a-zA-Z-_]+\))?.*$';
    const reg = new RegExp(regString);
    const match = reg.exec(commit.message);

    const type = options.hash['type'];
    const result = options.buffer();

    if (match) {
        const matchedType = match[1];
        if (type === matchedType) {
            result += options.fn()
            return result.toString();
        }

        if (type === 'other') {
            if (matchedType !== 'feat' && matchedType !== 'fix') {
                result += options.fn()
                return result.toString();
            }
        }
    }

    result += options.inverse()
    return result.toString();
});

/**
 * 将提交消息中的 JIRA 任务编号转换为超链接格式。
 *
 * @example
 * // 输入
 * {{withJiraLink "[JIRA-123] Fix a bug"}}
 *
 * // 输出
 * "[JIRA-123](http://pmc.chinastock.com.cn/jira/browse/JIRA-123) Fix a bug"
 */
Handlebars.registerHelper('withJiraLink', function(message) {
    var regString = '^\\[([a-zA-Z0-9-]+)\\]';
    var reg = new RegExp(regString);
    var match = reg.exec(message);

    if (match) {
        var jiraKey = match[1];
        var jiraUrl = 'http://pmc.chinastock.com.cn/jira/browse/' + jiraKey;
        // 使用正则替换原来的 [jiraKey]，防止嵌套
        var updatedMessage = message.replace(reg, '[' + jiraKey + '](' + jiraUrl + ')');

        return updatedMessage;
    }

    return message;
});
]]>
                            </javascriptHelper>
                            <templateContent>
                                <![CDATA[
# Galaxy Boot 发布日志

代码: https://gitlab.chinastock.com.cn/it-infrastructure/galaxy-boot

{{#tags}}
## [{{name}}] ({{tagDate .}})

### 新功能

  {{#commits}}
    {{#ifGalaxyCommitType . type='feat'}}
 - {{withJiraLink message}} ([{{hash}}](https://gitlab.chinastock.com.cn/it-infrastructure/galaxy-boot/commit/{{hashFull}}))
    {{/ifGalaxyCommitType}}
  {{/commits}}

### 缺陷

  {{#commits}}
    {{#ifGalaxyCommitType . type='fix'}}
 - {{withJiraLink message}} ([{{hash}}](https://gitlab.chinastock.com.cn/it-infrastructure/galaxy-boot/commit/{{hashFull}}))
    {{/ifGalaxyCommitType}}
  {{/commits}}

{{/tags}}
]]>
                            </templateContent>
                        </configuration>
                    </execution>
                </executions>
            </plugin>
            <!-- GitHook -->
            <plugin>
                <groupId>io.github.phillipuniverse</groupId>
                <artifactId>githook-maven-plugin</artifactId>
                <version>${githook-maven-plugin.version}</version>
                <inherited>false</inherited>
                <executions>
                    <execution>
                        <goals>
                            <goal>install</goal>
                        </goals>
                        <configuration>
                            <hooks>
                                <pre-push>
                                    exec mvn clean test package
                                </pre-push>
                            </hooks>
                        </configuration>
                    </execution>
                </executions>
                <configuration>
                    <!--是否跳过-->
                    <skip>false</skip>
                </configuration>
            </plugin>
            <!-- Jacoco 代码覆盖率检查 -->
            <plugin>
                <groupId>org.jacoco</groupId>
                <artifactId>jacoco-maven-plugin</artifactId>
                <version>${jacoco.version}</version>
                <configuration>
                    <includes>
                        <include>cn/com/chinastock/*/**</include>
                    </includes>
                    <excludes>
                        <exclude>cn/com/chinastock/cnf/docs/**</exclude>
                        <exclude>cn/com/chinastock/cnf/examples/**</exclude>
                    </excludes>
                    <rules>
                        <rule implementation="org.jacoco.maven.RuleConfiguration">
                            <element>BUNDLE</element>
                            <limits>
                                <!-- 指定方法覆盖最低值 -->
                                <limit implementation="org.jacoco.report.check.Limit">
                                    <counter>METHOD</counter>
                                    <value>COVEREDRATIO</value>
                                    <minimum>0.5</minimum>
                                </limit>
                                <!-- 指定指令覆盖最低值 -->
                                <limit implementation="org.jacoco.report.check.Limit">
                                    <counter>INSTRUCTION</counter>
                                    <value>COVEREDRATIO</value>
                                    <minimum>0.5</minimum>
                                </limit>
                            </limits>
                        </rule>
                    </rules>
                </configuration>
                <executions>
                    <execution>
                        <id>pre-test</id>
                        <goals>
                            <goal>prepare-agent</goal>
                        </goals>
                    </execution>
                    <execution>
                        <id>post-test</id>
                        <phase>test</phase>
                        <goals>
                            <goal>report</goal>
                            <goal>check</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
            <!-- git-build -->
            <plugin>
                <groupId>com.rudikershaw.gitbuildhook</groupId>
                <artifactId>git-build-hook-maven-plugin</artifactId>
                <version>${git-build-hook-maven-plugin.version}</version>
                <inherited>false</inherited>
                <configuration>
                    <installHooks>
                        <prepare-commit-msg>.mvn/.githooks/prepare-commit-msg</prepare-commit-msg>
                    </installHooks>
                </configuration>
                <executions>
                    <execution>
                        <goals>
                            <goal>install</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
            <plugin>
                <groupId>org.codehaus.mojo</groupId>
                <artifactId>exec-maven-plugin</artifactId>
                <version>${exec-maven-plugin.version}</version>
                <inherited>false</inherited>
                <executions>
                    <execution>
                        <id>script-chmod</id>
                        <phase>package</phase>
                        <goals>
                            <goal>exec</goal>
                        </goals>
                        <configuration>
                            <executable>chmod</executable>
                            <arguments>
                                <argument>755</argument>
                                <argument>${basedir}/.mvn/.githooks/prepare-commit-msg</argument>
                            </arguments>
                        </configuration>
                    </execution>
                </executions>
            </plugin>

            <plugin>
                <groupId>org.codehaus.mojo</groupId>
                <artifactId>build-helper-maven-plugin</artifactId>
                <version>${build-helper-maven-plugin.version}</version>
                <executions>
                    <execution>
                        <id>add-test-source</id>
                        <phase>generate-test-sources</phase>
                        <goals>
                            <goal>add-test-source</goal>
                        </goals>
                        <configuration>
                            <sources>
                                <source>src/integrationTest/java</source>
                            </sources>
                        </configuration>
                    </execution>
                </executions>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-deploy-plugin</artifactId>
                <version>${maven-deploy-plugin.version}</version>
            </plugin>
        </plugins>
    </build>

    <reporting>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-checkstyle-plugin</artifactId>
            </plugin>
        </plugins>
    </reporting>

    <profiles>
        <profile>
            <id>release</id>
            <build>
                <plugins>
                    <plugin>
                        <groupId>org.apache.maven.plugins</groupId>
                        <artifactId>maven-source-plugin</artifactId>
                        <version>${maven-source-plugin.version}</version>
                        <executions>
                            <execution>
                                <phase>package</phase>
                                <goals>
                                    <goal>jar-no-fork</goal>
                                </goals>
                            </execution>
                        </executions>
                    </plugin>
                    <plugin>
                        <groupId>org.apache.maven.plugins</groupId>
                        <artifactId>maven-javadoc-plugin</artifactId>
                        <version>${maven-javadoc-plugin.version}</version>
                        <executions>
                            <execution>
                                <phase>package</phase>
                                <goals>
                                    <goal>jar</goal>
                                </goals>
                            </execution>
                        </executions>
                    </plugin>

                </plugins>
            </build>
        </profile>
        <profile>
            <id>local</id>
            <distributionManagement>
                <repository>
                    <id>local.repo</id>
                    <name>Local Repository</name>
                    <url>file:${project.build.directory}/local-repo</url>
                </repository>
            </distributionManagement>
        </profile>
    </profiles>

    <distributionManagement>
        <repository>
            <id>release</id>
            <name>maven-release</name>
            <url>https://bkrepo.chinastock.com.cn/maven/n614c6/maven-release/</url>
        </repository>
    </distributionManagement>
</project>
